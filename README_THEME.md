# Healthy Restaurant App - Custom Theme Documentation

## Overview
This Flutter app features a comprehensive custom theme system designed for a healthy restaurant application with full Arabic and English language support, including RTL layout support.

## Features

### 🎨 Custom Theme System
- **Light and Dark Mode Support**: Automatic theme switching based on system preferences or manual selection
- **Material 3 Design**: Modern Material Design 3 components and styling
- **Custom Color Palette**: Healthy green-themed colors with proper contrast ratios
- **Typography System**: Locale-specific fonts (Cairo for Arabic, Poppins for English)

### 🌍 Internationalization
- **Bilingual Support**: English and Arabic languages
- **RTL Layout**: Proper right-to-left layout for Arabic
- **Locale-Specific Fonts**: Cairo font for Arabic text, Poppins for English
- **Dynamic Language Switching**: Change language without app restart

### 🏗️ Architecture
- **MVC Pattern**: Clean separation of concerns
- **Provider State Management**: Reactive state management
- **Custom Components**: Reusable UI components with consistent theming

## Theme Structure

### Color System
```dart
// Primary Colors - Fresh Green Theme
static const Color primary = Color(0xFF4CAF50);
static const Color primaryLight = Color(0xFF81C784);
static const Color primaryDark = Color(0xFF388E3C);

// Secondary Colors - Warm Orange
static const Color secondary = Color(0xFFFF9800);

// Food Category Colors
static const Color vegetarian = Color(0xFF8BC34A);
static const Color vegan = Color(0xFF4CAF50);
static const Color protein = Color(0xFFFF7043);
```

### Typography
- **Arabic Font**: Cairo (Regular, Bold, SemiBold, Light)
- **English Font**: Poppins (Regular, Bold, SemiBold, Light)
- **Responsive Text Styles**: Material 3 text theme with locale-specific fonts

### Custom Components

#### 1. CustomButton
```dart
CustomButton(
  text: 'Get Started',
  onPressed: () {},
  type: ButtonType.primary,
  size: ButtonSize.large,
  isFullWidth: true,
)
```

#### 2. CustomTextField
```dart
CustomTextField(
  label: 'Search',
  hint: 'Search for food...',
  prefixIcon: Icons.search,
  onChanged: (value) {},
)
```

#### 3. CustomCard & FoodCard
```dart
FoodCard(
  imageUrl: 'image_url',
  name: 'Food Name',
  description: 'Description',
  price: 12.99,
  rating: 4.8,
  reviewCount: 124,
  onTap: () {},
)
```

#### 4. CustomChip
```dart
FoodCategoryChip(
  category: 'Vegetarian',
  isSelected: true,
  onTap: () {},
)
```

#### 5. CustomBottomSheet
```dart
CustomBottomSheet.show(
  context: context,
  child: FilterBottomSheet(...),
)
```

## File Structure

```
lib/
├── core/
│   ├── theme/
│   │   ├── app_colors.dart          # Color definitions
│   │   ├── app_typography.dart      # Typography system
│   │   ├── app_theme.dart          # Main theme configuration
│   │   └── theme_controller.dart    # Theme state management
│   └── localization/
│       └── locale_controller.dart   # Language state management
├── data/
│   └── models/
│       ├── food_item.dart          # Food item model
│       └── restaurant.dart         # Restaurant model
├── presentation/
│   ├── controllers/
│   │   └── food_controller.dart    # Food data controller
│   ├── screens/
│   │   ├── splash/
│   │   │   └── splash_screen.dart  # Animated splash screen
│   │   ├── welcome/
│   │   │   └── welcome_screen.dart # Welcome screen
│   │   ├── onboarding/
│   │   │   └── onboarding_screen.dart # Onboarding flow
│   │   └── home/
│   │       └── home_screen.dart    # Main home screen
│   └── widgets/
│       ├── custom_button.dart      # Custom button component
│       ├── custom_text_field.dart  # Custom text field
│       ├── custom_card.dart        # Custom card components
│       ├── custom_chip.dart        # Custom chip components
│       └── custom_bottom_sheet.dart # Bottom sheet components
├── l10n/
│   ├── app_en.arb                  # English translations
│   └── app_ar.arb                  # Arabic translations
└── main.dart                       # App entry point
```

## Usage

### 1. Theme Controller
```dart
// Toggle theme
Provider.of<ThemeController>(context, listen: false).toggleTheme();

// Set specific theme
themeController.setThemeMode(ThemeMode.dark);
```

### 2. Locale Controller
```dart
// Toggle language
Provider.of<LocaleController>(context, listen: false).toggleLocale();

// Set specific language
localeController.setArabic();
localeController.setEnglish();
```

### 3. Food Controller
```dart
// Initialize data
Provider.of<FoodController>(context, listen: false).initialize();

// Apply filters
foodController.updateCategoryFilters(['Healthy', 'Vegan']);
foodController.updateSearchQuery('salad');
```

## Customization

### Adding New Colors
1. Add color definitions to `app_colors.dart`
2. Update theme configurations in `app_theme.dart`
3. Use colors in components

### Adding New Components
1. Create component in `presentation/widgets/`
2. Follow existing naming conventions
3. Use theme colors and typography
4. Support both light and dark themes

### Adding New Languages
1. Create new `.arb` file in `l10n/`
2. Add locale to `supportedLocales` in `main.dart`
3. Update `LocaleController` if needed

## Dependencies

```yaml
dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  provider: ^6.1.2
  intl: ^0.19.0
  google_fonts: ^6.2.1
  lottie: ^3.1.2
  shared_preferences: ^2.2.3
```

## Assets

### Fonts
- Place Cairo font files in `assets/fonts/`
- Place Poppins font files in `assets/fonts/`

### Images
- App icons: `assets/icons/`
- Food images: `assets/images/`
- Animations: `assets/animations/`

## Best Practices

1. **Consistent Theming**: Always use theme colors and typography
2. **Responsive Design**: Test on different screen sizes
3. **Accessibility**: Ensure proper contrast ratios and text sizes
4. **Performance**: Optimize images and animations
5. **Localization**: Test both LTR and RTL layouts

## Getting Started

1. Clone the repository
2. Run `flutter pub get`
3. Add font files to `assets/fonts/`
4. Run `flutter run`

## Contributing

1. Follow the established file structure
2. Use consistent naming conventions
3. Test both light/dark themes
4. Test both English/Arabic languages
5. Ensure responsive design
