class Restaurant {
  final String id;
  final String name;
  final String description;
  final String imageUrl;
  final List<String> images;
  final String address;
  final double latitude;
  final double longitude;
  final String phone;
  final String email;
  final double rating;
  final int reviewCount;
  final List<String> cuisineTypes;
  final List<String> specialties;
  final bool isOpen;
  final bool isDeliveryAvailable;
  final bool isPickupAvailable;
  final double deliveryFee;
  final int minDeliveryTime; // in minutes
  final int maxDeliveryTime; // in minutes
  final double minOrderAmount;
  final List<WorkingHours> workingHours;
  final bool isFeatured;
  final bool isVerified;
  final DateTime createdAt;
  final DateTime updatedAt;

  const Restaurant({
    required this.id,
    required this.name,
    required this.description,
    required this.imageUrl,
    this.images = const [],
    required this.address,
    required this.latitude,
    required this.longitude,
    required this.phone,
    required this.email,
    required this.rating,
    required this.reviewCount,
    this.cuisineTypes = const [],
    this.specialties = const [],
    this.isOpen = true,
    this.isDeliveryAvailable = true,
    this.isPickupAvailable = true,
    required this.deliveryFee,
    required this.minDeliveryTime,
    required this.maxDeliveryTime,
    required this.minOrderAmount,
    this.workingHours = const [],
    this.isFeatured = false,
    this.isVerified = false,
    required this.createdAt,
    required this.updatedAt,
  });

  String get deliveryTimeRange => '$minDeliveryTime-$maxDeliveryTime min';
  
  bool get hasDelivery => isDeliveryAvailable && isOpen;
  bool get hasPickup => isPickupAvailable && isOpen;

  factory Restaurant.fromJson(Map<String, dynamic> json) {
    return Restaurant(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      imageUrl: json['imageUrl'] as String,
      images: List<String>.from(json['images'] ?? []),
      address: json['address'] as String,
      latitude: (json['latitude'] as num).toDouble(),
      longitude: (json['longitude'] as num).toDouble(),
      phone: json['phone'] as String,
      email: json['email'] as String,
      rating: (json['rating'] as num).toDouble(),
      reviewCount: json['reviewCount'] as int,
      cuisineTypes: List<String>.from(json['cuisineTypes'] ?? []),
      specialties: List<String>.from(json['specialties'] ?? []),
      isOpen: json['isOpen'] as bool? ?? true,
      isDeliveryAvailable: json['isDeliveryAvailable'] as bool? ?? true,
      isPickupAvailable: json['isPickupAvailable'] as bool? ?? true,
      deliveryFee: (json['deliveryFee'] as num).toDouble(),
      minDeliveryTime: json['minDeliveryTime'] as int,
      maxDeliveryTime: json['maxDeliveryTime'] as int,
      minOrderAmount: (json['minOrderAmount'] as num).toDouble(),
      workingHours: (json['workingHours'] as List<dynamic>?)
          ?.map((e) => WorkingHours.fromJson(e as Map<String, dynamic>))
          .toList() ?? [],
      isFeatured: json['isFeatured'] as bool? ?? false,
      isVerified: json['isVerified'] as bool? ?? false,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'imageUrl': imageUrl,
      'images': images,
      'address': address,
      'latitude': latitude,
      'longitude': longitude,
      'phone': phone,
      'email': email,
      'rating': rating,
      'reviewCount': reviewCount,
      'cuisineTypes': cuisineTypes,
      'specialties': specialties,
      'isOpen': isOpen,
      'isDeliveryAvailable': isDeliveryAvailable,
      'isPickupAvailable': isPickupAvailable,
      'deliveryFee': deliveryFee,
      'minDeliveryTime': minDeliveryTime,
      'maxDeliveryTime': maxDeliveryTime,
      'minOrderAmount': minOrderAmount,
      'workingHours': workingHours.map((e) => e.toJson()).toList(),
      'isFeatured': isFeatured,
      'isVerified': isVerified,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Restaurant && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'Restaurant(id: $id, name: $name, rating: $rating)';
  }
}

class WorkingHours {
  final String dayOfWeek; // Monday, Tuesday, etc.
  final String openTime; // HH:mm format
  final String closeTime; // HH:mm format
  final bool isClosed;

  const WorkingHours({
    required this.dayOfWeek,
    required this.openTime,
    required this.closeTime,
    this.isClosed = false,
  });

  factory WorkingHours.fromJson(Map<String, dynamic> json) {
    return WorkingHours(
      dayOfWeek: json['dayOfWeek'] as String,
      openTime: json['openTime'] as String,
      closeTime: json['closeTime'] as String,
      isClosed: json['isClosed'] as bool? ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'dayOfWeek': dayOfWeek,
      'openTime': openTime,
      'closeTime': closeTime,
      'isClosed': isClosed,
    };
  }

  @override
  String toString() {
    if (isClosed) return '$dayOfWeek: Closed';
    return '$dayOfWeek: $openTime - $closeTime';
  }
}
