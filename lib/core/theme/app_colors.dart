import 'package:flutter/material.dart';

/// App color palette for healthy restaurant theme
class AppColors {
  AppColors._();

  // Primary Colors - Fresh Green Theme
  static const Color primary = Color(0xFF4CAF50); // Fresh green
  static const Color primaryLight = Color(0xFF81C784);
  static const Color primaryDark = Color(0xFF388E3C);
  static const Color primaryContainer = Color(0xFFE8F5E8);

  // Secondary Colors - Warm Orange
  static const Color secondary = Color(0xFFFF9800); // Warm orange
  static const Color secondaryLight = Color(0xFFFFB74D);
  static const Color secondaryDark = Color(0xFFF57C00);
  static const Color secondaryContainer = Color(0xFFFFF3E0);

  // Accent Colors
  static const Color accent = Color(0xFF2196F3); // Fresh blue
  static const Color accentLight = Color(0xFF64B5F6);
  static const Color accentDark = Color(0xFF1976D2);

  // Surface Colors - Light Theme
  static const Color surface = Color(0xFFFFFFFF);
  static const Color surfaceVariant = Color(0xFFF5F5F5);
  static const Color background = Color(0xFFFAFAFA);
  static const Color onPrimary = Color(0xFFFFFFFF);
  static const Color onSecondary = Color(0xFFFFFFFF);
  static const Color onSurface = Color(0xFF1C1B1F);
  static const Color onSurfaceVariant = Color(0xFF49454F);
  static const Color onBackground = Color(0xFF1C1B1F);

  // Surface Colors - Dark Theme
  static const Color surfaceDark = Color(0xFF121212);
  static const Color surfaceVariantDark = Color(0xFF2C2C2C);
  static const Color backgroundDark = Color(0xFF0F0F0F);
  static const Color onSurfaceDark = Color(0xFFE6E1E5);
  static const Color onSurfaceVariantDark = Color(0xFFCAC4D0);
  static const Color onBackgroundDark = Color(0xFFE6E1E5);

  // Utility Colors
  static const Color error = Color(0xFFB00020);
  static const Color success = Color(0xFF4CAF50);
  static const Color warning = Color(0xFFFF9800);
  static const Color info = Color(0xFF2196F3);

  // Border and Divider Colors
  static const Color border = Color(0xFFE0E0E0);
  static const Color borderDark = Color(0xFF3C3C3C);
  static const Color divider = Color(0xFFE0E0E0);
  static const Color dividerDark = Color(0xFF3C3C3C);

  // Shadow Colors
  static const Color shadow = Color(0x1A000000);
  static const Color shadowDark = Color(0x3A000000);

  // Neutral Colors
  static const Color neutral = Color(0xFF9E9E9E);
  static const Color neutralLight = Color(0xFFE0E0E0);
  static const Color neutralDark = Color(0xFF616161);

  // Food Category Colors
  static const Color vegetarian = Color(0xFF8BC34A);
  static const Color vegan = Color(0xFF4CAF50);
  static const Color protein = Color(0xFFFF7043);
  static const Color dairy = Color(0xFF42A5F5);
  static const Color grains = Color(0xFFFFB74D);
  static const Color fruits = Color(0xFFEC407A);

  // Gradient Colors
  static const LinearGradient primaryGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [primary, primaryDark],
  );

  static const LinearGradient secondaryGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [secondary, secondaryDark],
  );

  static const LinearGradient accentGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [accent, accentDark],
  );

  // Healthy food gradients
  static const LinearGradient healthyGradient = LinearGradient(
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    colors: [Color(0xFF81C784), Color(0xFF4CAF50)],
  );

  static const LinearGradient freshGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [Color(0xFF4CAF50), Color(0xFF8BC34A)],
  );

  // Rating colors
  static const Color ratingGold = Color(0xFFFFD700);
  static const Color ratingGray = Color(0xFFE0E0E0);

  // Status colors
  static const Color available = Color(0xFF4CAF50);
  static const Color unavailable = Color(0xFFE0E0E0);
  static const Color outOfStock = Color(0xFFFF5722);

  // Delivery status colors
  static const Color preparing = Color(0xFFFF9800);
  static const Color onTheWay = Color(0xFF2196F3);
  static const Color delivered = Color(0xFF4CAF50);
  static const Color cancelled = Color(0xFFB00020);
}
