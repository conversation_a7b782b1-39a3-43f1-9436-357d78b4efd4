import 'package:flutter/material.dart';

/// Typography system for the healthy restaurant app
/// Supports both Arabic (Cairo font) and English (Poppins font)
class AppTypography {
  AppTypography._();

  // Font families
  static const String arabicFontFamily = 'Cairo';
  static const String englishFontFamily = 'Poppins';

  /// Get appropriate font family based on locale
  static String getFontFamily(Locale locale) {
    return locale.languageCode == 'ar' ? arabicFontFamily : englishFontFamily;
  }

  /// Get text theme for specific locale
  static TextTheme getTextTheme(Locale locale, {bool isDark = false}) {
    final fontFamily = getFontFamily(locale);
    final baseColor = isDark ? Colors.white : Colors.black87;

    return TextTheme(
      // Display styles
      displayLarge: TextStyle(
        fontFamily: fontFamily,
        fontSize: 57,
        fontWeight: FontWeight.w400,
        letterSpacing: -0.25,
        color: baseColor,
        height: 1.12,
      ),
      displayMedium: TextStyle(
        fontFamily: fontFamily,
        fontSize: 45,
        fontWeight: FontWeight.w400,
        letterSpacing: 0,
        color: baseColor,
        height: 1.16,
      ),
      displaySmall: TextStyle(
        fontFamily: fontFamily,
        fontSize: 36,
        fontWeight: FontWeight.w400,
        letterSpacing: 0,
        color: baseColor,
        height: 1.22,
      ),

      // Headline styles
      headlineLarge: TextStyle(
        fontFamily: fontFamily,
        fontSize: 32,
        fontWeight: FontWeight.w400,
        letterSpacing: 0,
        color: baseColor,
        height: 1.25,
      ),
      headlineMedium: TextStyle(
        fontFamily: fontFamily,
        fontSize: 28,
        fontWeight: FontWeight.w400,
        letterSpacing: 0,
        color: baseColor,
        height: 1.29,
      ),
      headlineSmall: TextStyle(
        fontFamily: fontFamily,
        fontSize: 24,
        fontWeight: FontWeight.w400,
        letterSpacing: 0,
        color: baseColor,
        height: 1.33,
      ),

      // Title styles
      titleLarge: TextStyle(
        fontFamily: fontFamily,
        fontSize: 22,
        fontWeight: FontWeight.w400,
        letterSpacing: 0,
        color: baseColor,
        height: 1.27,
      ),
      titleMedium: TextStyle(
        fontFamily: fontFamily,
        fontSize: 16,
        fontWeight: FontWeight.w500,
        letterSpacing: 0.15,
        color: baseColor,
        height: 1.50,
      ),
      titleSmall: TextStyle(
        fontFamily: fontFamily,
        fontSize: 14,
        fontWeight: FontWeight.w500,
        letterSpacing: 0.1,
        color: baseColor,
        height: 1.43,
      ),

      // Body styles
      bodyLarge: TextStyle(
        fontFamily: fontFamily,
        fontSize: 16,
        fontWeight: FontWeight.w400,
        letterSpacing: 0.5,
        color: baseColor,
        height: 1.50,
      ),
      bodyMedium: TextStyle(
        fontFamily: fontFamily,
        fontSize: 14,
        fontWeight: FontWeight.w400,
        letterSpacing: 0.25,
        color: baseColor,
        height: 1.43,
      ),
      bodySmall: TextStyle(
        fontFamily: fontFamily,
        fontSize: 12,
        fontWeight: FontWeight.w400,
        letterSpacing: 0.4,
        color: baseColor,
        height: 1.33,
      ),

      // Label styles
      labelLarge: TextStyle(
        fontFamily: fontFamily,
        fontSize: 14,
        fontWeight: FontWeight.w500,
        letterSpacing: 0.1,
        color: baseColor,
        height: 1.43,
      ),
      labelMedium: TextStyle(
        fontFamily: fontFamily,
        fontSize: 12,
        fontWeight: FontWeight.w500,
        letterSpacing: 0.5,
        color: baseColor,
        height: 1.33,
      ),
      labelSmall: TextStyle(
        fontFamily: fontFamily,
        fontSize: 11,
        fontWeight: FontWeight.w500,
        letterSpacing: 0.5,
        color: baseColor,
        height: 1.45,
      ),
    );
  }

  /// Restaurant-specific text styles
  static TextStyle menuItemTitle(Locale locale, {bool isDark = false}) {
    return TextStyle(
      fontFamily: getFontFamily(locale),
      fontSize: 18,
      fontWeight: FontWeight.w600,
      color: isDark ? Colors.white : Colors.black87,
      height: 1.3,
    );
  }

  static TextStyle menuItemDescription(Locale locale, {bool isDark = false}) {
    return TextStyle(
      fontFamily: getFontFamily(locale),
      fontSize: 14,
      fontWeight: FontWeight.w400,
      color: isDark ? Colors.white70 : Colors.black54,
      height: 1.4,
    );
  }

  static TextStyle price(Locale locale, {bool isDark = false}) {
    return TextStyle(
      fontFamily: getFontFamily(locale),
      fontSize: 16,
      fontWeight: FontWeight.w700,
      color: isDark ? Colors.green[300] : Colors.green[700],
      height: 1.2,
    );
  }

  static TextStyle categoryTitle(Locale locale, {bool isDark = false}) {
    return TextStyle(
      fontFamily: getFontFamily(locale),
      fontSize: 20,
      fontWeight: FontWeight.w700,
      color: isDark ? Colors.white : Colors.black87,
      height: 1.2,
    );
  }

  static TextStyle nutritionLabel(Locale locale, {bool isDark = false}) {
    return TextStyle(
      fontFamily: getFontFamily(locale),
      fontSize: 12,
      fontWeight: FontWeight.w500,
      color: isDark ? Colors.white60 : Colors.black45,
      height: 1.3,
    );
  }
}
