import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// Controller for managing authentication state
class AuthController extends ChangeNotifier {
  static const String _isLoggedInKey = 'is_logged_in';
  static const String _userTokenKey = 'user_token';
  static const String _userDataKey = 'user_data';

  bool _isLoggedIn = false;
  bool _isLoading = false;
  String? _userToken;
  Map<String, dynamic>? _userData;
  String? _error;

  // Getters
  bool get isLoggedIn => _isLoggedIn;
  bool get isLoading => _isLoading;
  String? get userToken => _userToken;
  Map<String, dynamic>? get userData => _userData;
  String? get error => _error;

  /// Initialize authentication state from saved preferences
  Future<void> initializeAuth() async {
    _setLoading(true);
    try {
      final prefs = await SharedPreferences.getInstance();
      _isLoggedIn = prefs.getBool(_isLoggedInKey) ?? false;
      _userToken = prefs.getString(_userTokenKey);
      
      final userDataString = prefs.getString(_userDataKey);
      if (userDataString != null) {
        // In a real app, you would parse JSON here
        _userData = {'email': '<EMAIL>', 'name': 'User Name'};
      }
      
      notifyListeners();
    } catch (e) {
      _setError('Failed to initialize authentication: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Login with email and password
  Future<bool> login({
    required String email,
    required String password,
    bool rememberMe = false,
  }) async {
    _setLoading(true);
    _clearError();

    try {
      // Simulate API call
      await Future.delayed(const Duration(seconds: 2));

      // Simulate validation
      if (email.isEmpty || password.isEmpty) {
        throw Exception('Email and password are required');
      }

      if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email)) {
        throw Exception('Invalid email format');
      }

      if (password.length < 6) {
        throw Exception('Password must be at least 6 characters');
      }

      // Simulate successful login
      _userToken = 'mock_token_${DateTime.now().millisecondsSinceEpoch}';
      _userData = {
        'email': email,
        'name': 'User Name',
        'phone': '+1234567890',
        'avatar': null,
      };
      _isLoggedIn = true;

      // Save to preferences
      await _saveAuthData(rememberMe);

      notifyListeners();
      return true;
    } catch (e) {
      _setError(e.toString());
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Sign up with user details
  Future<bool> signup({
    required String name,
    required String email,
    required String phone,
    required String password,
  }) async {
    _setLoading(true);
    _clearError();

    try {
      // Simulate API call
      await Future.delayed(const Duration(seconds: 2));

      // Simulate validation
      if (name.isEmpty || email.isEmpty || phone.isEmpty || password.isEmpty) {
        throw Exception('All fields are required');
      }

      if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email)) {
        throw Exception('Invalid email format');
      }

      if (password.length < 6) {
        throw Exception('Password must be at least 6 characters');
      }

      if (phone.length < 10) {
        throw Exception('Invalid phone number');
      }

      // Simulate successful signup
      _userToken = 'mock_token_${DateTime.now().millisecondsSinceEpoch}';
      _userData = {
        'email': email,
        'name': name,
        'phone': phone,
        'avatar': null,
      };
      _isLoggedIn = true;

      // Save to preferences
      await _saveAuthData(true);

      notifyListeners();
      return true;
    } catch (e) {
      _setError(e.toString());
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Send password reset email
  Future<bool> forgotPassword({required String email}) async {
    _setLoading(true);
    _clearError();

    try {
      // Simulate API call
      await Future.delayed(const Duration(seconds: 2));

      // Simulate validation
      if (email.isEmpty) {
        throw Exception('Email is required');
      }

      if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email)) {
        throw Exception('Invalid email format');
      }

      // Simulate successful password reset request
      return true;
    } catch (e) {
      _setError(e.toString());
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Login with social provider (Google, Facebook, etc.)
  Future<bool> socialLogin({required String provider}) async {
    _setLoading(true);
    _clearError();

    try {
      // Simulate API call
      await Future.delayed(const Duration(seconds: 2));

      // Simulate successful social login
      _userToken = 'social_token_${DateTime.now().millisecondsSinceEpoch}';
      _userData = {
        'email': 'user@$provider.com',
        'name': 'Social User',
        'phone': null,
        'avatar': 'https://example.com/avatar.jpg',
        'provider': provider,
      };
      _isLoggedIn = true;

      // Save to preferences
      await _saveAuthData(true);

      notifyListeners();
      return true;
    } catch (e) {
      _setError(e.toString());
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Logout user
  Future<void> logout() async {
    _setLoading(true);

    try {
      // Simulate API call to invalidate token
      await Future.delayed(const Duration(seconds: 1));

      // Clear local data
      _isLoggedIn = false;
      _userToken = null;
      _userData = null;

      // Clear preferences
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_isLoggedInKey);
      await prefs.remove(_userTokenKey);
      await prefs.remove(_userDataKey);

      notifyListeners();
    } catch (e) {
      _setError('Failed to logout: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Update user profile
  Future<bool> updateProfile({
    String? name,
    String? phone,
    String? avatar,
  }) async {
    _setLoading(true);
    _clearError();

    try {
      // Simulate API call
      await Future.delayed(const Duration(seconds: 1));

      // Update local data
      if (_userData != null) {
        if (name != null) _userData!['name'] = name;
        if (phone != null) _userData!['phone'] = phone;
        if (avatar != null) _userData!['avatar'] = avatar;

        // Save updated data
        await _saveAuthData(true);
        notifyListeners();
      }

      return true;
    } catch (e) {
      _setError('Failed to update profile: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Change password
  Future<bool> changePassword({
    required String currentPassword,
    required String newPassword,
  }) async {
    _setLoading(true);
    _clearError();

    try {
      // Simulate API call
      await Future.delayed(const Duration(seconds: 2));

      // Simulate validation
      if (currentPassword.isEmpty || newPassword.isEmpty) {
        throw Exception('Both passwords are required');
      }

      if (newPassword.length < 6) {
        throw Exception('New password must be at least 6 characters');
      }

      // Simulate successful password change
      return true;
    } catch (e) {
      _setError(e.toString());
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Save authentication data to preferences
  Future<void> _saveAuthData(bool rememberMe) async {
    if (!rememberMe) return;

    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_isLoggedInKey, _isLoggedIn);
    if (_userToken != null) {
      await prefs.setString(_userTokenKey, _userToken!);
    }
    if (_userData != null) {
      // In a real app, you would serialize to JSON
      await prefs.setString(_userDataKey, _userData.toString());
    }
  }

  /// Helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String? error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
  }

  /// Check if user has specific permission
  bool hasPermission(String permission) {
    // Implement permission checking logic
    return _isLoggedIn;
  }

  /// Get user display name
  String get userDisplayName {
    if (_userData == null) return 'Guest';
    return _userData!['name'] ?? 'User';
  }

  /// Get user email
  String get userEmail {
    if (_userData == null) return '';
    return _userData!['email'] ?? '';
  }

  /// Get user avatar URL
  String? get userAvatar {
    if (_userData == null) return null;
    return _userData!['avatar'];
  }
}
