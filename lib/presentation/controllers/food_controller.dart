import 'package:flutter/material.dart';
import '../../data/models/food_item.dart';
import '../../data/models/restaurant.dart';

/// Controller for managing food items and restaurant data
class FoodController extends ChangeNotifier {
  List<FoodItem> _allFoodItems = [];
  List<FoodItem> _filteredFoodItems = [];
  List<Restaurant> _restaurants = [];
  List<FoodItem> _favoriteItems = [];
  List<FoodItem> _cartItems = [];
  
  bool _isLoading = false;
  String? _error;
  
  // Filter states
  List<String> _selectedCategories = [];
  String? _selectedPriceRange;
  double? _selectedMinRating;
  String _searchQuery = '';
  String _sortBy = 'rating'; // rating, price, name, delivery_time

  // Getters
  List<FoodItem> get allFoodItems => _allFoodItems;
  List<FoodItem> get filteredFoodItems => _filteredFoodItems;
  List<Restaurant> get restaurants => _restaurants;
  List<FoodItem> get favoriteItems => _favoriteItems;
  List<FoodItem> get cartItems => _cartItems;
  
  bool get isLoading => _isLoading;
  String? get error => _error;
  
  List<String> get selectedCategories => _selectedCategories;
  String? get selectedPriceRange => _selectedPriceRange;
  double? get selectedMinRating => _selectedMinRating;
  String get searchQuery => _searchQuery;
  String get sortBy => _sortBy;

  // Computed properties
  int get cartItemCount => _cartItems.length;
  double get cartTotal => _cartItems.fold(0, (sum, item) => sum + item.price);
  
  List<String> get availableCategories {
    return _allFoodItems
        .map((item) => item.category)
        .toSet()
        .toList()
        ..sort();
  }

  List<FoodItem> get featuredItems {
    return _allFoodItems.where((item) => item.isFeatured).toList();
  }

  List<FoodItem> get healthyItems {
    return _allFoodItems.where((item) => item.isHealthy).toList();
  }

  /// Initialize controller with sample data
  Future<void> initialize() async {
    _setLoading(true);
    try {
      await _loadSampleData();
      _applyFilters();
    } catch (e) {
      _setError('Failed to load data: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Load sample food data
  Future<void> _loadSampleData() async {
    // Simulate API delay
    await Future.delayed(const Duration(milliseconds: 500));

    _allFoodItems = [
      FoodItem(
        id: '1',
        name: 'Quinoa Buddha Bowl',
        description: 'Nutritious bowl with quinoa, roasted vegetables, avocado, and tahini dressing',
        price: 12.99,
        originalPrice: 15.99,
        imageUrl: 'https://example.com/quinoa-bowl.jpg',
        category: 'Healthy Bowls',
        tags: ['Vegan', 'Gluten-Free', 'High Protein'],
        ingredients: ['Quinoa', 'Avocado', 'Sweet Potato', 'Chickpeas', 'Tahini'],
        nutritionInfo: const NutritionInfo(
          calories: 450,
          protein: 15,
          carbs: 55,
          fat: 18,
          fiber: 12,
          sugar: 8,
          sodium: 320,
        ),
        rating: 4.8,
        reviewCount: 124,
        isAvailable: true,
        isFeatured: true,
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: true,
        preparationTime: 15,
        restaurantId: 'r1',
        restaurantName: 'Green Garden',
        createdAt: DateTime.now().subtract(const Duration(days: 30)),
        updatedAt: DateTime.now(),
      ),
      FoodItem(
        id: '2',
        name: 'Grilled Salmon Salad',
        description: 'Fresh Atlantic salmon with mixed greens, cherry tomatoes, and lemon vinaigrette',
        price: 16.99,
        imageUrl: 'https://example.com/salmon-salad.jpg',
        category: 'Salads',
        tags: ['High Protein', 'Omega-3', 'Low Carb'],
        ingredients: ['Salmon', 'Mixed Greens', 'Cherry Tomatoes', 'Cucumber', 'Lemon'],
        nutritionInfo: const NutritionInfo(
          calories: 380,
          protein: 32,
          carbs: 12,
          fat: 22,
          fiber: 6,
          sugar: 8,
          sodium: 280,
        ),
        rating: 4.6,
        reviewCount: 89,
        isAvailable: true,
        isFeatured: false,
        isVegetarian: false,
        isVegan: false,
        isGlutenFree: true,
        preparationTime: 12,
        restaurantId: 'r2',
        restaurantName: 'Ocean Fresh',
        createdAt: DateTime.now().subtract(const Duration(days: 15)),
        updatedAt: DateTime.now(),
      ),
      FoodItem(
        id: '3',
        name: 'Acai Smoothie Bowl',
        description: 'Antioxidant-rich acai bowl topped with fresh berries, granola, and coconut',
        price: 9.99,
        imageUrl: 'https://example.com/acai-bowl.jpg',
        category: 'Smoothie Bowls',
        tags: ['Antioxidants', 'Superfood', 'Breakfast'],
        ingredients: ['Acai', 'Blueberries', 'Strawberries', 'Granola', 'Coconut'],
        nutritionInfo: const NutritionInfo(
          calories: 320,
          protein: 8,
          carbs: 45,
          fat: 12,
          fiber: 10,
          sugar: 28,
          sodium: 45,
        ),
        rating: 4.7,
        reviewCount: 156,
        isAvailable: true,
        isFeatured: true,
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: false,
        preparationTime: 8,
        restaurantId: 'r3',
        restaurantName: 'Smoothie Paradise',
        createdAt: DateTime.now().subtract(const Duration(days: 7)),
        updatedAt: DateTime.now(),
      ),
    ];

    _restaurants = [
      Restaurant(
        id: 'r1',
        name: 'Green Garden',
        description: 'Organic and locally sourced healthy meals',
        imageUrl: 'https://example.com/green-garden.jpg',
        address: '123 Health St, Wellness City',
        latitude: 40.7128,
        longitude: -74.0060,
        phone: '+**********',
        email: '<EMAIL>',
        rating: 4.8,
        reviewCount: 245,
        cuisineTypes: ['Healthy', 'Vegan', 'Organic'],
        specialties: ['Buddha Bowls', 'Fresh Juices', 'Salads'],
        isOpen: true,
        deliveryFee: 2.99,
        minDeliveryTime: 25,
        maxDeliveryTime: 35,
        minOrderAmount: 15.0,
        isFeatured: true,
        isVerified: true,
        createdAt: DateTime.now().subtract(const Duration(days: 365)),
        updatedAt: DateTime.now(),
      ),
    ];
  }

  /// Apply current filters to food items
  void _applyFilters() {
    _filteredFoodItems = _allFoodItems.where((item) {
      // Category filter
      if (_selectedCategories.isNotEmpty && 
          !_selectedCategories.contains(item.category)) {
        return false;
      }

      // Rating filter
      if (_selectedMinRating != null && item.rating < _selectedMinRating!) {
        return false;
      }

      // Search query filter
      if (_searchQuery.isNotEmpty) {
        final query = _searchQuery.toLowerCase();
        if (!item.name.toLowerCase().contains(query) &&
            !item.description.toLowerCase().contains(query) &&
            !item.tags.any((tag) => tag.toLowerCase().contains(query))) {
          return false;
        }
      }

      return true;
    }).toList();

    _sortItems();
    notifyListeners();
  }

  /// Sort filtered items based on current sort criteria
  void _sortItems() {
    switch (_sortBy) {
      case 'rating':
        _filteredFoodItems.sort((a, b) => b.rating.compareTo(a.rating));
        break;
      case 'price_low':
        _filteredFoodItems.sort((a, b) => a.price.compareTo(b.price));
        break;
      case 'price_high':
        _filteredFoodItems.sort((a, b) => b.price.compareTo(a.price));
        break;
      case 'name':
        _filteredFoodItems.sort((a, b) => a.name.compareTo(b.name));
        break;
      case 'delivery_time':
        _filteredFoodItems.sort((a, b) => a.preparationTime.compareTo(b.preparationTime));
        break;
    }
  }

  /// Update search query and apply filters
  void updateSearchQuery(String query) {
    _searchQuery = query;
    _applyFilters();
  }

  /// Update category filters
  void updateCategoryFilters(List<String> categories) {
    _selectedCategories = categories;
    _applyFilters();
  }

  /// Update price range filter
  void updatePriceRangeFilter(String? priceRange) {
    _selectedPriceRange = priceRange;
    _applyFilters();
  }

  /// Update rating filter
  void updateRatingFilter(double? minRating) {
    _selectedMinRating = minRating;
    _applyFilters();
  }

  /// Update sort criteria
  void updateSortBy(String sortBy) {
    _sortBy = sortBy;
    _sortItems();
    notifyListeners();
  }

  /// Clear all filters
  void clearFilters() {
    _selectedCategories.clear();
    _selectedPriceRange = null;
    _selectedMinRating = null;
    _searchQuery = '';
    _applyFilters();
  }

  /// Toggle favorite status of a food item
  void toggleFavorite(String foodId) {
    final item = _allFoodItems.firstWhere((item) => item.id == foodId);
    
    if (_favoriteItems.contains(item)) {
      _favoriteItems.remove(item);
    } else {
      _favoriteItems.add(item);
    }
    
    notifyListeners();
  }

  /// Check if item is favorite
  bool isFavorite(String foodId) {
    return _favoriteItems.any((item) => item.id == foodId);
  }

  /// Add item to cart
  void addToCart(String foodId) {
    final item = _allFoodItems.firstWhere((item) => item.id == foodId);
    _cartItems.add(item);
    notifyListeners();
  }

  /// Remove item from cart
  void removeFromCart(String foodId) {
    _cartItems.removeWhere((item) => item.id == foodId);
    notifyListeners();
  }

  /// Clear cart
  void clearCart() {
    _cartItems.clear();
    notifyListeners();
  }

  /// Helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String? error) {
    _error = error;
    notifyListeners();
  }
}
