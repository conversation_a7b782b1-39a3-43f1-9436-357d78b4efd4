import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../core/theme/app_colors.dart';
import '../../../core/localization/locale_controller.dart';
import '../../../core/theme/theme_controller.dart';
import '../../controllers/food_controller.dart';
import '../../widgets/custom_button.dart';
import '../../widgets/custom_text_field.dart';
import '../../widgets/custom_card.dart';
import '../../widgets/custom_chip.dart';
import '../../widgets/custom_bottom_sheet.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    // Initialize food controller
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<FoodController>(context, listen: false).initialize();
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final localeController = Provider.of<LocaleController>(context);
    final themeController = Provider.of<ThemeController>(context);
    final foodController = Provider.of<FoodController>(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(
          localeController.isArabic ? 'هيلثي بايتس' : 'Healthy Bites',
        ),
        actions: [
          // Theme toggle
          IconButton(
            icon: Icon(
              themeController.isDarkMode ? Icons.light_mode : Icons.dark_mode,
            ),
            onPressed: () => themeController.toggleTheme(),
          ),
          // Language toggle
          IconButton(
            icon: const Icon(Icons.language),
            onPressed: () => localeController.toggleLocale(),
          ),
          // Cart
          Stack(
            children: [
              IconButton(
                icon: const Icon(Icons.shopping_cart),
                onPressed: () {
                  // Navigate to cart
                },
              ),
              if (foodController.cartItemCount > 0)
                Positioned(
                  right: 8,
                  top: 8,
                  child: Container(
                    padding: const EdgeInsets.all(2),
                    decoration: const BoxDecoration(
                      color: AppColors.error,
                      shape: BoxShape.circle,
                    ),
                    constraints: const BoxConstraints(
                      minWidth: 16,
                      minHeight: 16,
                    ),
                    child: Text(
                      '${foodController.cartItemCount}',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ),
            ],
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: () => foodController.initialize(),
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Welcome section
              _buildWelcomeSection(context, localeController),
              
              const SizedBox(height: 24),
              
              // Search bar
              _buildSearchSection(context, localeController, foodController),
              
              const SizedBox(height: 24),
              
              // Categories
              _buildCategoriesSection(context, localeController, foodController),
              
              const SizedBox(height: 24),
              
              // Featured items
              _buildFeaturedSection(context, localeController, foodController),
              
              const SizedBox(height: 24),
              
              // All items
              _buildAllItemsSection(context, localeController, foodController),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildWelcomeSection(BuildContext context, LocaleController localeController) {
    final theme = Theme.of(context);
    
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: AppColors.healthyGradient,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  localeController.isArabic 
                      ? 'مرحباً بك!' 
                      : 'Welcome!',
                  style: theme.textTheme.headlineSmall?.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  localeController.isArabic
                      ? 'اكتشف أشهى الأطباق الصحية'
                      : 'Discover delicious healthy meals',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: Colors.white.withOpacity(0.9),
                  ),
                ),
              ],
            ),
          ),
          const Icon(
            Icons.restaurant_menu,
            size: 48,
            color: Colors.white,
          ),
        ],
      ),
    );
  }

  Widget _buildSearchSection(
    BuildContext context, 
    LocaleController localeController, 
    FoodController foodController,
  ) {
    return Row(
      children: [
        Expanded(
          child: CustomTextField(
            controller: _searchController,
            hint: localeController.isArabic 
                ? 'ابحث عن الطعام...' 
                : 'Search for food...',
            prefixIcon: Icons.search,
            onChanged: (value) => foodController.updateSearchQuery(value),
          ),
        ),
        const SizedBox(width: 12),
        CustomButton(
          text: '',
          icon: Icons.filter_list,
          onPressed: () => _showFilterBottomSheet(context, foodController),
          type: ButtonType.outline,
          size: ButtonSize.medium,
        ),
      ],
    );
  }

  Widget _buildCategoriesSection(
    BuildContext context, 
    LocaleController localeController, 
    FoodController foodController,
  ) {
    final theme = Theme.of(context);
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          localeController.isArabic ? 'الفئات' : 'Categories',
          style: theme.textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        SizedBox(
          height: 40,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: foodController.availableCategories.length,
            itemBuilder: (context, index) {
              final category = foodController.availableCategories[index];
              final isSelected = foodController.selectedCategories.contains(category);
              
              return Padding(
                padding: const EdgeInsets.only(right: 8),
                child: FoodCategoryChip(
                  category: category,
                  isSelected: isSelected,
                  onTap: () {
                    final newCategories = List<String>.from(foodController.selectedCategories);
                    if (isSelected) {
                      newCategories.remove(category);
                    } else {
                      newCategories.add(category);
                    }
                    foodController.updateCategoryFilters(newCategories);
                  },
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildFeaturedSection(
    BuildContext context, 
    LocaleController localeController, 
    FoodController foodController,
  ) {
    final theme = Theme.of(context);
    
    if (foodController.featuredItems.isEmpty) {
      return const SizedBox.shrink();
    }
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          localeController.isArabic ? 'مميز' : 'Featured',
          style: theme.textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        SizedBox(
          height: 280,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: foodController.featuredItems.length,
            itemBuilder: (context, index) {
              final item = foodController.featuredItems[index];
              
              return Container(
                width: 250,
                margin: const EdgeInsets.only(right: 16),
                child: FoodCard(
                  imageUrl: item.imageUrl,
                  name: item.name,
                  description: item.description,
                  price: item.price,
                  originalPrice: item.originalPrice,
                  rating: item.rating,
                  reviewCount: item.reviewCount,
                  isFavorite: foodController.isFavorite(item.id),
                  isAvailable: item.isAvailable,
                  tags: item.tags,
                  onTap: () => _showFoodDetails(context, item),
                  onFavoriteToggle: () => foodController.toggleFavorite(item.id),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildAllItemsSection(
    BuildContext context, 
    LocaleController localeController, 
    FoodController foodController,
  ) {
    final theme = Theme.of(context);
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              localeController.isArabic ? 'جميع الأطباق' : 'All Items',
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const Spacer(),
            CustomButton(
              text: localeController.isArabic ? 'ترتيب' : 'Sort',
              icon: Icons.sort,
              onPressed: () => _showSortBottomSheet(context, foodController),
              type: ButtonType.text,
              size: ButtonSize.small,
            ),
          ],
        ),
        const SizedBox(height: 12),
        
        if (foodController.isLoading)
          const Center(
            child: Padding(
              padding: EdgeInsets.all(32),
              child: CircularProgressIndicator(),
            ),
          )
        else if (foodController.filteredFoodItems.isEmpty)
          Center(
            child: Padding(
              padding: const EdgeInsets.all(32),
              child: Text(
                localeController.isArabic 
                    ? 'لا توجد عناصر' 
                    : 'No items found',
                style: theme.textTheme.bodyLarge?.copyWith(
                  color: AppColors.onSurfaceVariant,
                ),
              ),
            ),
          )
        else
          GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              childAspectRatio: 0.75,
              crossAxisSpacing: 16,
              mainAxisSpacing: 16,
            ),
            itemCount: foodController.filteredFoodItems.length,
            itemBuilder: (context, index) {
              final item = foodController.filteredFoodItems[index];
              
              return FoodCard(
                imageUrl: item.imageUrl,
                name: item.name,
                description: item.description,
                price: item.price,
                originalPrice: item.originalPrice,
                rating: item.rating,
                reviewCount: item.reviewCount,
                isFavorite: foodController.isFavorite(item.id),
                isAvailable: item.isAvailable,
                tags: item.tags,
                onTap: () => _showFoodDetails(context, item),
                onFavoriteToggle: () => foodController.toggleFavorite(item.id),
              );
            },
          ),
      ],
    );
  }

  void _showFilterBottomSheet(BuildContext context, FoodController foodController) {
    CustomBottomSheet.show(
      context: context,
      isScrollControlled: true,
      child: FilterBottomSheet(
        categories: foodController.availableCategories,
        selectedCategories: foodController.selectedCategories,
        priceRanges: const ['\$0-10', '\$10-20', '\$20-30', '\$30+'],
        selectedPriceRange: foodController.selectedPriceRange,
        ratings: const [3.0, 3.5, 4.0, 4.5],
        selectedRating: foodController.selectedMinRating,
        onApplyFilters: ({categories, priceRange, rating}) {
          if (categories != null) {
            foodController.updateCategoryFilters(categories);
          }
          if (priceRange != null) {
            foodController.updatePriceRangeFilter(priceRange);
          }
          if (rating != null) {
            foodController.updateRatingFilter(rating);
          }
        },
      ),
    );
  }

  void _showSortBottomSheet(BuildContext context, FoodController foodController) {
    CustomBottomSheet.show(
      context: context,
      child: SortBottomSheet(
        sortOptions: const [
          'Rating (High to Low)',
          'Price (Low to High)',
          'Price (High to Low)',
          'Name (A to Z)',
          'Delivery Time',
        ],
        selectedSort: _getSortDisplayName(foodController.sortBy),
        onSortChanged: (sortOption) {
          final sortBy = _getSortKey(sortOption);
          if (sortBy != null) {
            foodController.updateSortBy(sortBy);
          }
        },
      ),
    );
  }

  void _showFoodDetails(BuildContext context, dynamic item) {
    CustomBottomSheet.show(
      context: context,
      isScrollControlled: true,
      child: FoodDetailsBottomSheet(
        foodName: item.name,
        description: item.description,
        price: item.price,
        ingredients: item.ingredients,
        nutritionInfo: {
          'calories': item.nutritionInfo.calories,
          'protein': item.nutritionInfo.protein,
          'carbs': item.nutritionInfo.carbs,
          'fat': item.nutritionInfo.fat,
        },
        onAddToCart: () {
          Provider.of<FoodController>(context, listen: false).addToCart(item.id);
          Navigator.of(context).pop();
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('${item.name} added to cart'),
              backgroundColor: AppColors.success,
            ),
          );
        },
      ),
    );
  }

  String? _getSortDisplayName(String sortKey) {
    switch (sortKey) {
      case 'rating': return 'Rating (High to Low)';
      case 'price_low': return 'Price (Low to High)';
      case 'price_high': return 'Price (High to Low)';
      case 'name': return 'Name (A to Z)';
      case 'delivery_time': return 'Delivery Time';
      default: return null;
    }
  }

  String? _getSortKey(String? displayName) {
    switch (displayName) {
      case 'Rating (High to Low)': return 'rating';
      case 'Price (Low to High)': return 'price_low';
      case 'Price (High to Low)': return 'price_high';
      case 'Name (A to Z)': return 'name';
      case 'Delivery Time': return 'delivery_time';
      default: return null;
    }
  }
}
