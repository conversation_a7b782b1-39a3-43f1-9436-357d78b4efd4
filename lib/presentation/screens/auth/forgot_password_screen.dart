import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../core/theme/app_colors.dart';
import '../../../core/localization/locale_controller.dart';
import '../../widgets/custom_button.dart';
import '../../widgets/custom_text_field.dart';
import 'login_screen.dart';

class ForgotPasswordScreen extends StatefulWidget {
  const ForgotPasswordScreen({super.key});

  @override
  State<ForgotPasswordScreen> createState() => _ForgotPasswordScreenState();
}

class _ForgotPasswordScreenState extends State<ForgotPasswordScreen> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  bool _isLoading = false;
  bool _emailSent = false;

  @override
  void dispose() {
    _emailController.dispose();
    super.dispose();
  }

  Future<void> _sendResetEmail() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    // Simulate API call
    await Future.delayed(const Duration(seconds: 2));

    setState(() {
      _isLoading = false;
      _emailSent = true;
    });
  }

  Future<void> _resendEmail() async {
    setState(() {
      _isLoading = true;
    });

    // Simulate API call
    await Future.delayed(const Duration(seconds: 1));

    setState(() {
      _isLoading = false;
    });

    final localeController = Provider.of<LocaleController>(context, listen: false);
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          localeController.isArabic 
              ? 'تم إرسال البريد الإلكتروني مرة أخرى' 
              : 'Email sent again',
        ),
        backgroundColor: AppColors.success,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final localeController = Provider.of<LocaleController>(context);

    return Scaffold(
      body: Container(
        width: double.infinity,
        height: double.infinity,
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              AppColors.accent,
              AppColors.accentLight,
              Colors.white,
            ],
            stops: [0.0, 0.3, 0.7],
          ),
        ),
        child: SafeArea(
          child: SingleChildScrollView(
            padding: const EdgeInsets.symmetric(horizontal: 24),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(height: 40),

                // Back button
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(
                    Icons.arrow_back,
                    color: Colors.white,
                  ),
                ),

                const SizedBox(height: 20),

                // Header section
                _buildHeader(context, localeController, theme),

                const SizedBox(height: 60),

                // Main content
                Container(
                  padding: const EdgeInsets.all(24),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(20),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.1),
                        blurRadius: 20,
                        offset: const Offset(0, 10),
                      ),
                    ],
                  ),
                  child: _emailSent 
                      ? _buildEmailSentContent(context, localeController, theme)
                      : _buildEmailForm(context, localeController, theme),
                ),

                const SizedBox(height: 40),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context, LocaleController localeController, ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // App logo
        Container(
          width: 80,
          height: 80,
          decoration: BoxDecoration(
            color: Colors.white,
            shape: BoxShape.circle,
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 20,
                offset: const Offset(0, 10),
              ),
            ],
          ),
          child: const Icon(
            Icons.lock_reset,
            size: 40,
            color: AppColors.accent,
          ),
        ),

        const SizedBox(height: 24),

        // Title
        Text(
          localeController.isArabic ? 'نسيت كلمة المرور؟' : 'Forgot Password?',
          style: theme.textTheme.headlineMedium?.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),

        const SizedBox(height: 8),

        Text(
          localeController.isArabic 
              ? 'لا تقلق، سنساعدك في استعادة حسابك' 
              : "Don't worry, we'll help you recover your account",
          style: theme.textTheme.bodyLarge?.copyWith(
            color: Colors.white.withOpacity(0.9),
          ),
        ),
      ],
    );
  }

  Widget _buildEmailForm(BuildContext context, LocaleController localeController, ThemeData theme) {
    return Form(
      key: _formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Instruction text
          Text(
            localeController.isArabic 
                ? 'أدخل بريدك الإلكتروني وسنرسل لك رابط إعادة تعيين كلمة المرور' 
                : 'Enter your email address and we\'ll send you a password reset link',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: AppColors.onSurfaceVariant,
              height: 1.5,
            ),
          ),

          const SizedBox(height: 32),

          // Email field
          CustomTextField(
            controller: _emailController,
            label: localeController.isArabic ? 'البريد الإلكتروني' : 'Email Address',
            hint: localeController.isArabic 
                ? 'أدخل بريدك الإلكتروني' 
                : 'Enter your email address',
            prefixIcon: Icons.email_outlined,
            keyboardType: TextInputType.emailAddress,
            validator: (value) {
              if (value == null || value.isEmpty) {
                return localeController.isArabic 
                    ? 'البريد الإلكتروني مطلوب' 
                    : 'Email is required';
              }
              if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value)) {
                return localeController.isArabic 
                    ? 'البريد الإلكتروني غير صحيح' 
                    : 'Invalid email format';
              }
              return null;
            },
          ),

          const SizedBox(height: 32),

          // Send reset email button
          CustomButton(
            text: localeController.isArabic ? 'إرسال رابط الاستعادة' : 'Send Reset Link',
            onPressed: _sendResetEmail,
            type: ButtonType.primary,
            size: ButtonSize.large,
            isFullWidth: true,
            isLoading: _isLoading,
            backgroundColor: AppColors.accent,
          ),

          const SizedBox(height: 24),

          // Back to login
          Center(
            child: TextButton(
              onPressed: () {
                Navigator.of(context).pushReplacement(
                  MaterialPageRoute(
                    builder: (context) => const LoginScreen(),
                  ),
                );
              },
              child: Text(
                localeController.isArabic ? 'العودة لتسجيل الدخول' : 'Back to Login',
                style: TextStyle(
                  color: AppColors.accent,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmailSentContent(BuildContext context, LocaleController localeController, ThemeData theme) {
    return Column(
      children: [
        // Success icon
        Container(
          width: 80,
          height: 80,
          decoration: BoxDecoration(
            color: AppColors.success.withOpacity(0.1),
            shape: BoxShape.circle,
          ),
          child: const Icon(
            Icons.mark_email_read,
            size: 40,
            color: AppColors.success,
          ),
        ),

        const SizedBox(height: 24),

        // Success title
        Text(
          localeController.isArabic ? 'تم إرسال البريد!' : 'Email Sent!',
          style: theme.textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
            color: AppColors.success,
          ),
        ),

        const SizedBox(height: 16),

        // Success message
        Text(
          localeController.isArabic 
              ? 'تم إرسال رابط إعادة تعيين كلمة المرور إلى بريدك الإلكتروني. تحقق من صندوق الوارد والبريد المزعج.' 
              : 'A password reset link has been sent to your email address. Please check your inbox and spam folder.',
          style: theme.textTheme.bodyMedium?.copyWith(
            color: AppColors.onSurfaceVariant,
            height: 1.5,
          ),
          textAlign: TextAlign.center,
        ),

        const SizedBox(height: 32),

        // Resend email button
        CustomButton(
          text: localeController.isArabic ? 'إعادة الإرسال' : 'Resend Email',
          onPressed: _resendEmail,
          type: ButtonType.outline,
          size: ButtonSize.large,
          isFullWidth: true,
          isLoading: _isLoading,
          borderColor: AppColors.accent,
          textColor: AppColors.accent,
        ),

        const SizedBox(height: 16),

        // Back to login
        CustomButton(
          text: localeController.isArabic ? 'العودة لتسجيل الدخول' : 'Back to Login',
          onPressed: () {
            Navigator.of(context).pushReplacement(
              MaterialPageRoute(
                builder: (context) => const LoginScreen(),
              ),
            );
          },
          type: ButtonType.primary,
          size: ButtonSize.large,
          isFullWidth: true,
          backgroundColor: AppColors.accent,
        ),

        const SizedBox(height: 24),

        // Help text
        Text(
          localeController.isArabic 
              ? 'لم تستلم البريد؟ تحقق من مجلد البريد المزعج أو جرب عنوان بريد إلكتروني آخر.' 
              : "Didn't receive the email? Check your spam folder or try a different email address.",
          style: theme.textTheme.bodySmall?.copyWith(
            color: AppColors.onSurfaceVariant,
            height: 1.4,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }
}
