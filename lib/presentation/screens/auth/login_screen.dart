import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../core/theme/app_colors.dart';
import '../../../core/localization/locale_controller.dart';
import '../../widgets/custom_button.dart';
import '../../widgets/custom_text_field.dart';
import 'signup_screen.dart';
import 'forgot_password_screen.dart';
import '../home/<USER>';

class LoginScreen extends StatefulWidget {
  const LoginScreen({super.key});

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  bool _isLoading = false;
  bool _rememberMe = false;

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  Future<void> _login() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    // Simulate API call
    await Future.delayed(const Duration(seconds: 2));

    setState(() {
      _isLoading = false;
    });

    // Navigate to home screen
    if (mounted) {
      Navigator.of(context).pushReplacement(
        MaterialPageRoute(
          builder: (context) => const HomeScreen(),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final localeController = Provider.of<LocaleController>(context);
    final size = MediaQuery.of(context).size;

    return Scaffold(
      body: Container(
        width: double.infinity,
        height: double.infinity,
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              AppColors.primary,
              AppColors.primaryLight,
              Colors.white,
            ],
            stops: [0.0, 0.3, 0.7],
          ),
        ),
        child: SafeArea(
          child: SingleChildScrollView(
            padding: const EdgeInsets.symmetric(horizontal: 24),
            child: Form(
              key: _formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const SizedBox(height: 40),

                  // Back button
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(
                      Icons.arrow_back,
                      color: Colors.white,
                    ),
                  ),

                  const SizedBox(height: 20),

                  // Header section
                  _buildHeader(context, localeController, theme),

                  const SizedBox(height: 60),

                  // Login form
                  Container(
                    padding: const EdgeInsets.all(24),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(20),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.1),
                          blurRadius: 20,
                          offset: const Offset(0, 10),
                        ),
                      ],
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Email field
                        CustomTextField(
                          controller: _emailController,
                          label: localeController.isArabic ? 'البريد الإلكتروني' : 'Email',
                          hint: localeController.isArabic 
                              ? 'أدخل بريدك الإلكتروني' 
                              : 'Enter your email',
                          prefixIcon: Icons.email_outlined,
                          keyboardType: TextInputType.emailAddress,
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return localeController.isArabic 
                                  ? 'البريد الإلكتروني مطلوب' 
                                  : 'Email is required';
                            }
                            if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value)) {
                              return localeController.isArabic 
                                  ? 'البريد الإلكتروني غير صحيح' 
                                  : 'Invalid email format';
                            }
                            return null;
                          },
                        ),

                        const SizedBox(height: 20),

                        // Password field
                        CustomTextField(
                          controller: _passwordController,
                          label: localeController.isArabic ? 'كلمة المرور' : 'Password',
                          hint: localeController.isArabic 
                              ? 'أدخل كلمة المرور' 
                              : 'Enter your password',
                          prefixIcon: Icons.lock_outlined,
                          obscureText: true,
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return localeController.isArabic 
                                  ? 'كلمة المرور مطلوبة' 
                                  : 'Password is required';
                            }
                            if (value.length < 6) {
                              return localeController.isArabic 
                                  ? 'كلمة المرور يجب أن تكون 6 أحرف على الأقل' 
                                  : 'Password must be at least 6 characters';
                            }
                            return null;
                          },
                        ),

                        const SizedBox(height: 16),

                        // Remember me and forgot password
                        Row(
                          children: [
                            Checkbox(
                              value: _rememberMe,
                              onChanged: (value) {
                                setState(() {
                                  _rememberMe = value ?? false;
                                });
                              },
                              activeColor: AppColors.primary,
                            ),
                            Text(
                              localeController.isArabic ? 'تذكرني' : 'Remember me',
                              style: theme.textTheme.bodyMedium,
                            ),
                            const Spacer(),
                            TextButton(
                              onPressed: () {
                                Navigator.of(context).push(
                                  MaterialPageRoute(
                                    builder: (context) => const ForgotPasswordScreen(),
                                  ),
                                );
                              },
                              child: Text(
                                localeController.isArabic 
                                    ? 'نسيت كلمة المرور؟' 
                                    : 'Forgot Password?',
                                style: TextStyle(
                                  color: AppColors.primary,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                          ],
                        ),

                        const SizedBox(height: 32),

                        // Login button
                        CustomButton(
                          text: localeController.isArabic ? 'تسجيل الدخول' : 'Login',
                          onPressed: _login,
                          type: ButtonType.primary,
                          size: ButtonSize.large,
                          isFullWidth: true,
                          isLoading: _isLoading,
                        ),

                        const SizedBox(height: 24),

                        // Divider
                        Row(
                          children: [
                            const Expanded(child: Divider()),
                            Padding(
                              padding: const EdgeInsets.symmetric(horizontal: 16),
                              child: Text(
                                localeController.isArabic ? 'أو' : 'OR',
                                style: theme.textTheme.bodyMedium?.copyWith(
                                  color: AppColors.onSurfaceVariant,
                                ),
                              ),
                            ),
                            const Expanded(child: Divider()),
                          ],
                        ),

                        const SizedBox(height: 24),

                        // Social login buttons
                        _buildSocialLoginButtons(context, localeController),

                        const SizedBox(height: 32),

                        // Sign up link
                        Center(
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Text(
                                localeController.isArabic 
                                    ? 'ليس لديك حساب؟ ' 
                                    : "Don't have an account? ",
                                style: theme.textTheme.bodyMedium,
                              ),
                              TextButton(
                                onPressed: () {
                                  Navigator.of(context).pushReplacement(
                                    MaterialPageRoute(
                                      builder: (context) => const SignupScreen(),
                                    ),
                                  );
                                },
                                child: Text(
                                  localeController.isArabic ? 'إنشاء حساب' : 'Sign Up',
                                  style: TextStyle(
                                    color: AppColors.primary,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),

                  const SizedBox(height: 40),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context, LocaleController localeController, ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // App logo
        Container(
          width: 80,
          height: 80,
          decoration: BoxDecoration(
            color: Colors.white,
            shape: BoxShape.circle,
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 20,
                offset: const Offset(0, 10),
              ),
            ],
          ),
          child: const Icon(
            Icons.restaurant_menu,
            size: 40,
            color: AppColors.primary,
          ),
        ),

        const SizedBox(height: 24),

        // Welcome text
        Text(
          localeController.isArabic ? 'مرحباً بعودتك!' : 'Welcome Back!',
          style: theme.textTheme.headlineMedium?.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),

        const SizedBox(height: 8),

        Text(
          localeController.isArabic 
              ? 'سجل دخولك للمتابعة' 
              : 'Sign in to continue',
          style: theme.textTheme.bodyLarge?.copyWith(
            color: Colors.white.withOpacity(0.9),
          ),
        ),
      ],
    );
  }

  Widget _buildSocialLoginButtons(BuildContext context, LocaleController localeController) {
    return Column(
      children: [
        // Google login
        CustomButton(
          text: localeController.isArabic 
              ? 'تسجيل الدخول بجوجل' 
              : 'Continue with Google',
          onPressed: () {
            // Implement Google login
          },
          type: ButtonType.outline,
          size: ButtonSize.large,
          isFullWidth: true,
          icon: Icons.g_mobiledata,
        ),

        const SizedBox(height: 12),

        // Facebook login
        CustomButton(
          text: localeController.isArabic 
              ? 'تسجيل الدخول بفيسبوك' 
              : 'Continue with Facebook',
          onPressed: () {
            // Implement Facebook login
          },
          type: ButtonType.outline,
          size: ButtonSize.large,
          isFullWidth: true,
          icon: Icons.facebook,
        ),
      ],
    );
  }
}
