import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../core/theme/app_colors.dart';
import '../../../core/localization/locale_controller.dart';
import '../../widgets/custom_button.dart';
import '../../widgets/custom_text_field.dart';
import 'login_screen.dart';
import '../home/<USER>';

class SignupScreen extends StatefulWidget {
  const SignupScreen({super.key});

  @override
  State<SignupScreen> createState() => _SignupScreenState();
}

class _SignupScreenState extends State<SignupScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _emailController = TextEditingController();
  final _phoneController = TextEditingController();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  bool _isLoading = false;
  bool _acceptTerms = false;

  @override
  void dispose() {
    _nameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  Future<void> _signup() async {
    if (!_formKey.currentState!.validate()) return;

    if (!_acceptTerms) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            Provider.of<LocaleController>(context, listen: false).isArabic
                ? 'يجب الموافقة على الشروط والأحكام'
                : 'Please accept terms and conditions',
          ),
          backgroundColor: AppColors.error,
        ),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    // Simulate API call
    await Future.delayed(const Duration(seconds: 2));

    setState(() {
      _isLoading = false;
    });

    // Navigate to home screen
    if (mounted) {
      Navigator.of(context).pushReplacement(
        MaterialPageRoute(
          builder: (context) => const HomeScreen(),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final localeController = Provider.of<LocaleController>(context);

    return Scaffold(
      body: Container(
        width: double.infinity,
        height: double.infinity,
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              AppColors.secondary,
              AppColors.secondaryLight,
              Colors.white,
            ],
            stops: [0.0, 0.3, 0.7],
          ),
        ),
        child: SafeArea(
          child: SingleChildScrollView(
            padding: const EdgeInsets.symmetric(horizontal: 24),
            child: Form(
              key: _formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const SizedBox(height: 40),

                  // Back button
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(
                      Icons.arrow_back,
                      color: Colors.white,
                    ),
                  ),

                  const SizedBox(height: 20),

                  // Header section
                  _buildHeader(context, localeController, theme),

                  const SizedBox(height: 40),

                  // Signup form
                  Container(
                    padding: const EdgeInsets.all(24),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(20),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.1),
                          blurRadius: 20,
                          offset: const Offset(0, 10),
                        ),
                      ],
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Full name field
                        CustomTextField(
                          controller: _nameController,
                          label: localeController.isArabic ? 'الاسم الكامل' : 'Full Name',
                          hint: localeController.isArabic 
                              ? 'أدخل اسمك الكامل' 
                              : 'Enter your full name',
                          prefixIcon: Icons.person_outlined,
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return localeController.isArabic 
                                  ? 'الاسم مطلوب' 
                                  : 'Name is required';
                            }
                            if (value.length < 2) {
                              return localeController.isArabic 
                                  ? 'الاسم يجب أن يكون حرفين على الأقل' 
                                  : 'Name must be at least 2 characters';
                            }
                            return null;
                          },
                        ),

                        const SizedBox(height: 20),

                        // Email field
                        CustomTextField(
                          controller: _emailController,
                          label: localeController.isArabic ? 'البريد الإلكتروني' : 'Email',
                          hint: localeController.isArabic 
                              ? 'أدخل بريدك الإلكتروني' 
                              : 'Enter your email',
                          prefixIcon: Icons.email_outlined,
                          keyboardType: TextInputType.emailAddress,
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return localeController.isArabic 
                                  ? 'البريد الإلكتروني مطلوب' 
                                  : 'Email is required';
                            }
                            if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value)) {
                              return localeController.isArabic 
                                  ? 'البريد الإلكتروني غير صحيح' 
                                  : 'Invalid email format';
                            }
                            return null;
                          },
                        ),

                        const SizedBox(height: 20),

                        // Phone field
                        CustomTextField(
                          controller: _phoneController,
                          label: localeController.isArabic ? 'رقم الهاتف' : 'Phone Number',
                          hint: localeController.isArabic 
                              ? 'أدخل رقم هاتفك' 
                              : 'Enter your phone number',
                          prefixIcon: Icons.phone_outlined,
                          keyboardType: TextInputType.phone,
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return localeController.isArabic 
                                  ? 'رقم الهاتف مطلوب' 
                                  : 'Phone number is required';
                            }
                            if (value.length < 10) {
                              return localeController.isArabic 
                                  ? 'رقم الهاتف غير صحيح' 
                                  : 'Invalid phone number';
                            }
                            return null;
                          },
                        ),

                        const SizedBox(height: 20),

                        // Password field
                        CustomTextField(
                          controller: _passwordController,
                          label: localeController.isArabic ? 'كلمة المرور' : 'Password',
                          hint: localeController.isArabic 
                              ? 'أدخل كلمة المرور' 
                              : 'Enter your password',
                          prefixIcon: Icons.lock_outlined,
                          obscureText: true,
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return localeController.isArabic 
                                  ? 'كلمة المرور مطلوبة' 
                                  : 'Password is required';
                            }
                            if (value.length < 6) {
                              return localeController.isArabic 
                                  ? 'كلمة المرور يجب أن تكون 6 أحرف على الأقل' 
                                  : 'Password must be at least 6 characters';
                            }
                            return null;
                          },
                        ),

                        const SizedBox(height: 20),

                        // Confirm password field
                        CustomTextField(
                          controller: _confirmPasswordController,
                          label: localeController.isArabic ? 'تأكيد كلمة المرور' : 'Confirm Password',
                          hint: localeController.isArabic 
                              ? 'أعد إدخال كلمة المرور' 
                              : 'Re-enter your password',
                          prefixIcon: Icons.lock_outlined,
                          obscureText: true,
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return localeController.isArabic 
                                  ? 'تأكيد كلمة المرور مطلوب' 
                                  : 'Confirm password is required';
                            }
                            if (value != _passwordController.text) {
                              return localeController.isArabic 
                                  ? 'كلمة المرور غير متطابقة' 
                                  : 'Passwords do not match';
                            }
                            return null;
                          },
                        ),

                        const SizedBox(height: 16),

                        // Terms and conditions
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Checkbox(
                              value: _acceptTerms,
                              onChanged: (value) {
                                setState(() {
                                  _acceptTerms = value ?? false;
                                });
                              },
                              activeColor: AppColors.secondary,
                            ),
                            Expanded(
                              child: RichText(
                                text: TextSpan(
                                  style: theme.textTheme.bodyMedium,
                                  children: [
                                    TextSpan(
                                      text: localeController.isArabic 
                                          ? 'أوافق على ' 
                                          : 'I agree to the ',
                                    ),
                                    TextSpan(
                                      text: localeController.isArabic 
                                          ? 'الشروط والأحكام' 
                                          : 'Terms & Conditions',
                                      style: TextStyle(
                                        color: AppColors.secondary,
                                        fontWeight: FontWeight.w600,
                                      ),
                                    ),
                                    TextSpan(
                                      text: localeController.isArabic 
                                          ? ' و' 
                                          : ' and ',
                                    ),
                                    TextSpan(
                                      text: localeController.isArabic 
                                          ? 'سياسة الخصوصية' 
                                          : 'Privacy Policy',
                                      style: TextStyle(
                                        color: AppColors.secondary,
                                        fontWeight: FontWeight.w600,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ],
                        ),

                        const SizedBox(height: 32),

                        // Signup button
                        CustomButton(
                          text: localeController.isArabic ? 'إنشاء حساب' : 'Create Account',
                          onPressed: _signup,
                          type: ButtonType.primary,
                          size: ButtonSize.large,
                          isFullWidth: true,
                          isLoading: _isLoading,
                          backgroundColor: AppColors.secondary,
                        ),

                        const SizedBox(height: 24),

                        // Divider
                        Row(
                          children: [
                            const Expanded(child: Divider()),
                            Padding(
                              padding: const EdgeInsets.symmetric(horizontal: 16),
                              child: Text(
                                localeController.isArabic ? 'أو' : 'OR',
                                style: theme.textTheme.bodyMedium?.copyWith(
                                  color: AppColors.onSurfaceVariant,
                                ),
                              ),
                            ),
                            const Expanded(child: Divider()),
                          ],
                        ),

                        const SizedBox(height: 24),

                        // Social signup buttons
                        _buildSocialSignupButtons(context, localeController),

                        const SizedBox(height: 32),

                        // Login link
                        Center(
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Text(
                                localeController.isArabic 
                                    ? 'لديك حساب بالفعل؟ ' 
                                    : "Already have an account? ",
                                style: theme.textTheme.bodyMedium,
                              ),
                              TextButton(
                                onPressed: () {
                                  Navigator.of(context).pushReplacement(
                                    MaterialPageRoute(
                                      builder: (context) => const LoginScreen(),
                                    ),
                                  );
                                },
                                child: Text(
                                  localeController.isArabic ? 'تسجيل الدخول' : 'Login',
                                  style: TextStyle(
                                    color: AppColors.secondary,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),

                  const SizedBox(height: 40),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context, LocaleController localeController, ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // App logo
        Container(
          width: 80,
          height: 80,
          decoration: BoxDecoration(
            color: Colors.white,
            shape: BoxShape.circle,
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 20,
                offset: const Offset(0, 10),
              ),
            ],
          ),
          child: const Icon(
            Icons.restaurant_menu,
            size: 40,
            color: AppColors.secondary,
          ),
        ),

        const SizedBox(height: 24),

        // Welcome text
        Text(
          localeController.isArabic ? 'إنشاء حساب جديد' : 'Create Account',
          style: theme.textTheme.headlineMedium?.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),

        const SizedBox(height: 8),

        Text(
          localeController.isArabic 
              ? 'انضم إلينا واستمتع بأفضل الأطباق الصحية' 
              : 'Join us and enjoy the best healthy meals',
          style: theme.textTheme.bodyLarge?.copyWith(
            color: Colors.white.withOpacity(0.9),
          ),
        ),
      ],
    );
  }

  Widget _buildSocialSignupButtons(BuildContext context, LocaleController localeController) {
    return Column(
      children: [
        // Google signup
        CustomButton(
          text: localeController.isArabic 
              ? 'التسجيل بجوجل' 
              : 'Sign up with Google',
          onPressed: () {
            // Implement Google signup
          },
          type: ButtonType.outline,
          size: ButtonSize.large,
          isFullWidth: true,
          icon: Icons.g_mobiledata,
          borderColor: AppColors.secondary,
          textColor: AppColors.secondary,
        ),

        const SizedBox(height: 12),

        // Facebook signup
        CustomButton(
          text: localeController.isArabic 
              ? 'التسجيل بفيسبوك' 
              : 'Sign up with Facebook',
          onPressed: () {
            // Implement Facebook signup
          },
          type: ButtonType.outline,
          size: ButtonSize.large,
          isFullWidth: true,
          icon: Icons.facebook,
          borderColor: AppColors.secondary,
          textColor: AppColors.secondary,
        ),
      ],
    );
  }
}
