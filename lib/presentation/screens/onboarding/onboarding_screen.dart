import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../core/theme/app_colors.dart';
import '../../../core/localization/locale_controller.dart';
import '../../widgets/custom_button.dart';
import '../home/<USER>';

class OnboardingScreen extends StatefulWidget {
  const OnboardingScreen({super.key});

  @override
  State<OnboardingScreen> createState() => _OnboardingScreenState();
}

class _OnboardingScreenState extends State<OnboardingScreen> {
  final PageController _pageController = PageController();
  int _currentPage = 0;

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  void _nextPage() {
    if (_currentPage < 2) {
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    } else {
      // Navigate to main app
      _completeOnboarding();
    }
  }

  void _previousPage() {
    if (_currentPage > 0) {
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _completeOnboarding() {
    // Navigate to main app (home screen)
    Navigator.of(context).pushReplacement(
      MaterialPageRoute(
        builder: (context) => const HomeScreen(),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final localeController = Provider.of<LocaleController>(context);
    
    return Scaffold(
      body: SafeArea(
        child: Column(
          children: [
            // Skip button
            Align(
              alignment: Alignment.topRight,
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: TextButton(
                  onPressed: _completeOnboarding,
                  child: Text(
                    localeController.isArabic ? 'تخطي' : 'Skip',
                    style: const TextStyle(
                      color: AppColors.onSurfaceVariant,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ),
            ),

            // Page view
            Expanded(
              child: PageView(
                controller: _pageController,
                onPageChanged: (index) {
                  setState(() {
                    _currentPage = index;
                  });
                },
                children: [
                  _buildOnboardingPage(
                    context,
                    icon: Icons.restaurant_menu,
                    title: localeController.isArabic 
                        ? 'اكتشف أطباق صحية'
                        : 'Discover Healthy Meals',
                    description: localeController.isArabic
                        ? 'تصفح مجموعة واسعة من الأطباق الصحية واللذيذة من أفضل المطاعم'
                        : 'Browse a wide variety of healthy and delicious meals from top restaurants',
                    color: AppColors.primary,
                  ),
                  _buildOnboardingPage(
                    context,
                    icon: Icons.delivery_dining,
                    title: localeController.isArabic 
                        ? 'توصيل سريع'
                        : 'Fast Delivery',
                    description: localeController.isArabic
                        ? 'احصل على طعامك المفضل بسرعة وأمان إلى باب منزلك'
                        : 'Get your favorite food delivered quickly and safely to your doorstep',
                    color: AppColors.secondary,
                  ),
                  _buildOnboardingPage(
                    context,
                    icon: Icons.favorite,
                    title: localeController.isArabic 
                        ? 'نمط حياة صحي'
                        : 'Healthy Lifestyle',
                    description: localeController.isArabic
                        ? 'ابدأ رحلتك نحو نمط حياة صحي مع خيارات طعام متوازنة ومغذية'
                        : 'Start your journey to a healthy lifestyle with balanced and nutritious food options',
                    color: AppColors.accent,
                  ),
                ],
              ),
            ),

            // Page indicators and navigation
            Padding(
              padding: const EdgeInsets.all(24),
              child: Column(
                children: [
                  // Page indicators
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: List.generate(
                      3,
                      (index) => Container(
                        margin: const EdgeInsets.symmetric(horizontal: 4),
                        width: _currentPage == index ? 24 : 8,
                        height: 8,
                        decoration: BoxDecoration(
                          color: _currentPage == index
                              ? AppColors.primary
                              : AppColors.border,
                          borderRadius: BorderRadius.circular(4),
                        ),
                      ),
                    ),
                  ),

                  const SizedBox(height: 32),

                  // Navigation buttons
                  Row(
                    children: [
                      if (_currentPage > 0)
                        Expanded(
                          child: CustomButton(
                            text: localeController.isArabic ? 'السابق' : 'Previous',
                            onPressed: _previousPage,
                            type: ButtonType.outline,
                            size: ButtonSize.large,
                          ),
                        ),
                      
                      if (_currentPage > 0) const SizedBox(width: 16),
                      
                      Expanded(
                        child: CustomButton(
                          text: _currentPage == 2
                              ? (localeController.isArabic ? 'ابدأ' : 'Get Started')
                              : (localeController.isArabic ? 'التالي' : 'Next'),
                          onPressed: _nextPage,
                          type: ButtonType.primary,
                          size: ButtonSize.large,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOnboardingPage(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String description,
    required Color color,
  }) {
    final theme = Theme.of(context);
    final size = MediaQuery.of(context).size;

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 24),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Icon with gradient background
          Container(
            width: size.width * 0.6,
            height: size.width * 0.6,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  color.withOpacity(0.1),
                  color.withOpacity(0.05),
                ],
              ),
              shape: BoxShape.circle,
              border: Border.all(
                color: color.withOpacity(0.2),
                width: 2,
              ),
            ),
            child: Icon(
              icon,
              size: 80,
              color: color,
            ),
          ),

          const SizedBox(height: 48),

          // Title
          Text(
            title,
            style: theme.textTheme.headlineMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: AppColors.onSurface,
            ),
            textAlign: TextAlign.center,
          ),

          const SizedBox(height: 24),

          // Description
          Text(
            description,
            style: theme.textTheme.bodyLarge?.copyWith(
              color: AppColors.onSurfaceVariant,
              height: 1.6,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
