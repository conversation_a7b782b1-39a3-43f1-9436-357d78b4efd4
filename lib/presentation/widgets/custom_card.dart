import 'package:flutter/material.dart';
import '../../core/theme/app_colors.dart';

enum CardType { elevated, outlined, filled }

class CustomCard extends StatelessWidget {
  final Widget child;
  final CardType type;
  final double? elevation;
  final Color? backgroundColor;
  final Color? borderColor;
  final double? borderRadius;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final VoidCallback? onTap;
  final bool isSelected;
  final Gradient? gradient;

  const CustomCard({
    super.key,
    required this.child,
    this.type = CardType.elevated,
    this.elevation,
    this.backgroundColor,
    this.borderColor,
    this.borderRadius,
    this.padding,
    this.margin,
    this.onTap,
    this.isSelected = false,
    this.gradient,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    Widget cardContent = Container(
      padding: padding ?? const EdgeInsets.all(16),
      decoration: _buildDecoration(colorScheme),
      child: child,
    );

    if (onTap != null) {
      cardContent = InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(borderRadius ?? 12),
        child: cardContent,
      );
    }

    return Container(
      margin: margin ?? const EdgeInsets.all(8),
      decoration: type == CardType.elevated
          ? BoxDecoration(
              borderRadius: BorderRadius.circular(borderRadius ?? 12),
              boxShadow: [
                BoxShadow(
                  color: AppColors.shadow,
                  blurRadius: elevation ?? 4,
                  offset: const Offset(0, 2),
                ),
              ],
            )
          : null,
      child: ClipRRect(
        borderRadius: BorderRadius.circular(borderRadius ?? 12),
        child: cardContent,
      ),
    );
  }

  BoxDecoration _buildDecoration(ColorScheme colorScheme) {
    Color defaultBackgroundColor;
    
    switch (type) {
      case CardType.elevated:
        defaultBackgroundColor = colorScheme.surface;
        break;
      case CardType.outlined:
        defaultBackgroundColor = Colors.transparent;
        break;
      case CardType.filled:
        defaultBackgroundColor = colorScheme.surfaceVariant;
        break;
    }

    return BoxDecoration(
      color: gradient == null ? (backgroundColor ?? defaultBackgroundColor) : null,
      gradient: gradient,
      border: type == CardType.outlined || isSelected
          ? Border.all(
              color: isSelected 
                  ? AppColors.primary 
                  : (borderColor ?? AppColors.border),
              width: isSelected ? 2 : 1,
            )
          : null,
      borderRadius: BorderRadius.circular(borderRadius ?? 12),
    );
  }
}

class FoodCard extends StatelessWidget {
  final String imageUrl;
  final String name;
  final String description;
  final double price;
  final double? originalPrice;
  final double rating;
  final int reviewCount;
  final bool isFavorite;
  final bool isAvailable;
  final VoidCallback? onTap;
  final VoidCallback? onFavoriteToggle;
  final List<String>? tags;

  const FoodCard({
    super.key,
    required this.imageUrl,
    required this.name,
    required this.description,
    required this.price,
    this.originalPrice,
    required this.rating,
    required this.reviewCount,
    this.isFavorite = false,
    this.isAvailable = true,
    this.onTap,
    this.onFavoriteToggle,
    this.tags,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return CustomCard(
      onTap: isAvailable ? onTap : null,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Image section
          Stack(
            children: [
              ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: AspectRatio(
                  aspectRatio: 16 / 9,
                  child: Image.network(
                    imageUrl,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) {
                      return Container(
                        color: AppColors.surfaceVariant,
                        child: const Icon(
                          Icons.restaurant,
                          size: 48,
                          color: AppColors.onSurfaceVariant,
                        ),
                      );
                    },
                  ),
                ),
              ),
              
              // Favorite button
              Positioned(
                top: 8,
                right: 8,
                child: GestureDetector(
                  onTap: onFavoriteToggle,
                  child: Container(
                    padding: const EdgeInsets.all(6),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.9),
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      isFavorite ? Icons.favorite : Icons.favorite_border,
                      color: isFavorite ? AppColors.error : AppColors.onSurfaceVariant,
                      size: 20,
                    ),
                  ),
                ),
              ),
              
              // Availability overlay
              if (!isAvailable)
                Positioned.fill(
                  child: Container(
                    decoration: BoxDecoration(
                      color: Colors.black.withOpacity(0.5),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Center(
                      child: Text(
                        'Out of Stock',
                        style: TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                ),
            ],
          ),
          
          const SizedBox(height: 12),
          
          // Content section
          Text(
            name,
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
          
          const SizedBox(height: 4),
          
          Text(
            description,
            style: theme.textTheme.bodySmall?.copyWith(
              color: AppColors.onSurfaceVariant,
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
          
          const SizedBox(height: 8),
          
          // Tags
          if (tags != null && tags!.isNotEmpty) ...[
            Wrap(
              spacing: 4,
              children: tags!.take(2).map((tag) => Chip(
                label: Text(
                  tag,
                  style: theme.textTheme.labelSmall,
                ),
                backgroundColor: AppColors.primaryContainer,
                padding: EdgeInsets.zero,
                materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
              )).toList(),
            ),
            const SizedBox(height: 8),
          ],
          
          // Rating and price row
          Row(
            children: [
              Icon(
                Icons.star,
                size: 16,
                color: AppColors.ratingGold,
              ),
              const SizedBox(width: 4),
              Text(
                rating.toString(),
                style: theme.textTheme.bodySmall?.copyWith(
                  fontWeight: FontWeight.w500,
                ),
              ),
              Text(
                ' (${reviewCount})',
                style: theme.textTheme.bodySmall?.copyWith(
                  color: AppColors.onSurfaceVariant,
                ),
              ),
              
              const Spacer(),
              
              // Price
              if (originalPrice != null) ...[
                Text(
                  '\$${originalPrice!.toStringAsFixed(2)}',
                  style: theme.textTheme.bodySmall?.copyWith(
                    decoration: TextDecoration.lineThrough,
                    color: AppColors.onSurfaceVariant,
                  ),
                ),
                const SizedBox(width: 4),
              ],
              Text(
                '\$${price.toStringAsFixed(2)}',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: AppColors.primary,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
