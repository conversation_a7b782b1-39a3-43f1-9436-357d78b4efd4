import 'package:flutter/material.dart';
import '../../core/theme/app_colors.dart';
import 'custom_button.dart';
import 'custom_chip.dart';

class CustomBottomSheet {
  static Future<T?> show<T>({
    required BuildContext context,
    required Widget child,
    bool isDismissible = true,
    bool enableDrag = true,
    double? height,
    bool isScrollControlled = false,
  }) {
    return showModalBottomSheet<T>(
      context: context,
      isDismissible: isDismissible,
      enableDrag: enableDrag,
      isScrollControlled: isScrollControlled,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: height,
        decoration: const BoxDecoration(
          color: AppColors.surface,
          borderRadius: BorderRadius.vertical(
            top: Radius.circular(20),
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Handle bar
            Container(
              margin: const EdgeInsets.only(top: 12),
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: AppColors.onSurfaceVariant.withOpacity(0.4),
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            Flexible(child: child),
          ],
        ),
      ),
    );
  }
}

class FilterBottomSheet extends StatefulWidget {
  final List<String> categories;
  final List<String> selectedCategories;
  final List<String> priceRanges;
  final String? selectedPriceRange;
  final List<double> ratings;
  final double? selectedRating;
  final Function({
    List<String>? categories,
    String? priceRange,
    double? rating,
  }) onApplyFilters;

  const FilterBottomSheet({
    super.key,
    required this.categories,
    required this.selectedCategories,
    required this.priceRanges,
    this.selectedPriceRange,
    required this.ratings,
    this.selectedRating,
    required this.onApplyFilters,
  });

  @override
  State<FilterBottomSheet> createState() => _FilterBottomSheetState();
}

class _FilterBottomSheetState extends State<FilterBottomSheet> {
  late List<String> _selectedCategories;
  String? _selectedPriceRange;
  double? _selectedRating;

  @override
  void initState() {
    super.initState();
    _selectedCategories = List.from(widget.selectedCategories);
    _selectedPriceRange = widget.selectedPriceRange;
    _selectedRating = widget.selectedRating;
  }

  void _clearFilters() {
    setState(() {
      _selectedCategories.clear();
      _selectedPriceRange = null;
      _selectedRating = null;
    });
  }

  void _applyFilters() {
    widget.onApplyFilters(
      categories: _selectedCategories,
      priceRange: _selectedPriceRange,
      rating: _selectedRating,
    );
    Navigator.of(context).pop();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Padding(
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          // Header
          Row(
            children: [
              Text(
                'Filters',
                style: theme.textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const Spacer(),
              TextButton(
                onPressed: _clearFilters,
                child: const Text(
                  'Clear All',
                  style: TextStyle(color: AppColors.error),
                ),
              ),
            ],
          ),

          const SizedBox(height: 24),

          // Categories
          FilterChipGroup(
            title: 'Categories',
            options: widget.categories,
            selectedOptions: _selectedCategories,
            onSelectionChanged: (selected) {
              setState(() {
                _selectedCategories = selected;
              });
            },
            multiSelect: true,
          ),

          const SizedBox(height: 24),

          // Price Range
          Text(
            'Price Range',
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 12),
          Wrap(
            spacing: 8,
            children: widget.priceRanges.map((range) {
              return PriceRangeChip(
                priceRange: range,
                isSelected: _selectedPriceRange == range,
                onTap: () {
                  setState(() {
                    _selectedPriceRange = 
                        _selectedPriceRange == range ? null : range;
                  });
                },
              );
            }).toList(),
          ),

          const SizedBox(height: 24),

          // Rating
          Text(
            'Minimum Rating',
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 12),
          Wrap(
            spacing: 8,
            children: widget.ratings.map((rating) {
              return RatingChip(
                rating: rating,
                isSelected: _selectedRating == rating,
                onTap: () {
                  setState(() {
                    _selectedRating = 
                        _selectedRating == rating ? null : rating;
                  });
                },
              );
            }).toList(),
          ),

          const SizedBox(height: 32),

          // Action buttons
          Row(
            children: [
              Expanded(
                child: CustomButton(
                  text: 'Reset',
                  onPressed: _clearFilters,
                  type: ButtonType.outline,
                  size: ButtonSize.large,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: CustomButton(
                  text: 'Apply Filters',
                  onPressed: _applyFilters,
                  type: ButtonType.primary,
                  size: ButtonSize.large,
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),
        ],
      ),
    );
  }
}

class SortBottomSheet extends StatefulWidget {
  final List<String> sortOptions;
  final String? selectedSort;
  final Function(String?) onSortChanged;

  const SortBottomSheet({
    super.key,
    required this.sortOptions,
    this.selectedSort,
    required this.onSortChanged,
  });

  @override
  State<SortBottomSheet> createState() => _SortBottomSheetState();
}

class _SortBottomSheetState extends State<SortBottomSheet> {
  String? _selectedSort;

  @override
  void initState() {
    super.initState();
    _selectedSort = widget.selectedSort;
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Padding(
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            'Sort By',
            style: theme.textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),

          const SizedBox(height: 24),

          ...widget.sortOptions.map((option) {
            return RadioListTile<String>(
              title: Text(option),
              value: option,
              groupValue: _selectedSort,
              onChanged: (value) {
                setState(() {
                  _selectedSort = value;
                });
                widget.onSortChanged(value);
                Navigator.of(context).pop();
              },
              activeColor: AppColors.primary,
              contentPadding: EdgeInsets.zero,
            );
          }).toList(),

          const SizedBox(height: 16),
        ],
      ),
    );
  }
}

class FoodDetailsBottomSheet extends StatelessWidget {
  final String foodName;
  final String description;
  final double price;
  final List<String> ingredients;
  final Map<String, dynamic> nutritionInfo;
  final VoidCallback? onAddToCart;

  const FoodDetailsBottomSheet({
    super.key,
    required this.foodName,
    required this.description,
    required this.price,
    required this.ingredients,
    required this.nutritionInfo,
    this.onAddToCart,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Padding(
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          // Food name and price
          Row(
            children: [
              Expanded(
                child: Text(
                  foodName,
                  style: theme.textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              Text(
                '\$${price.toStringAsFixed(2)}',
                style: theme.textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: AppColors.primary,
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // Description
          Text(
            description,
            style: theme.textTheme.bodyMedium?.copyWith(
              color: AppColors.onSurfaceVariant,
              height: 1.5,
            ),
          ),

          const SizedBox(height: 24),

          // Ingredients
          Text(
            'Ingredients',
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 8),
          Wrap(
            spacing: 8,
            runSpacing: 4,
            children: ingredients.map((ingredient) {
              return CustomChip(
                label: ingredient,
                backgroundColor: AppColors.primaryContainer,
                textColor: AppColors.primary,
                size: ChipSize.small,
              );
            }).toList(),
          ),

          const SizedBox(height: 24),

          // Nutrition info
          Text(
            'Nutrition Information',
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 12),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AppColors.surfaceVariant,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildNutritionItem('Calories', '${nutritionInfo['calories']}'),
                _buildNutritionItem('Protein', '${nutritionInfo['protein']}g'),
                _buildNutritionItem('Carbs', '${nutritionInfo['carbs']}g'),
                _buildNutritionItem('Fat', '${nutritionInfo['fat']}g'),
              ],
            ),
          ),

          const SizedBox(height: 32),

          // Add to cart button
          CustomButton(
            text: 'Add to Cart',
            onPressed: onAddToCart,
            type: ButtonType.primary,
            size: ButtonSize.large,
            isFullWidth: true,
            icon: Icons.shopping_cart,
          ),

          const SizedBox(height: 16),
        ],
      ),
    );
  }

  Widget _buildNutritionItem(String label, String value) {
    return Column(
      children: [
        Text(
          value,
          style: const TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 16,
            color: AppColors.primary,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: const TextStyle(
            fontSize: 12,
            color: AppColors.onSurfaceVariant,
          ),
        ),
      ],
    );
  }
}
