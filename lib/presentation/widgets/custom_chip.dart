import 'package:flutter/material.dart';
import '../../core/theme/app_colors.dart';

enum ChipType { filter, choice, action, input }
enum ChipSize { small, medium, large }

class CustomChip extends StatelessWidget {
  final String label;
  final bool isSelected;
  final VoidCallback? onTap;
  final VoidCallback? onDeleted;
  final IconData? icon;
  final IconData? deleteIcon;
  final ChipType type;
  final ChipSize size;
  final Color? backgroundColor;
  final Color? selectedColor;
  final Color? textColor;
  final Color? selectedTextColor;
  final double? borderRadius;

  const CustomChip({
    super.key,
    required this.label,
    this.isSelected = false,
    this.onTap,
    this.onDeleted,
    this.icon,
    this.deleteIcon,
    this.type = ChipType.filter,
    this.size = ChipSize.medium,
    this.backgroundColor,
    this.selectedColor,
    this.textColor,
    this.selectedTextColor,
    this.borderRadius,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    // Size configurations
    EdgeInsets padding;
    double fontSize;
    double iconSize;

    switch (size) {
      case ChipSize.small:
        padding = const EdgeInsets.symmetric(horizontal: 8, vertical: 4);
        fontSize = 11;
        iconSize = 14;
        break;
      case ChipSize.medium:
        padding = const EdgeInsets.symmetric(horizontal: 12, vertical: 6);
        fontSize = 12;
        iconSize = 16;
        break;
      case ChipSize.large:
        padding = const EdgeInsets.symmetric(horizontal: 16, vertical: 8);
        fontSize = 14;
        iconSize = 18;
        break;
    }

    // Color configurations
    Color chipBackgroundColor;
    Color chipTextColor;

    if (isSelected) {
      chipBackgroundColor = selectedColor ?? AppColors.primary;
      chipTextColor = selectedTextColor ?? AppColors.onPrimary;
    } else {
      chipBackgroundColor = backgroundColor ?? AppColors.surfaceVariant;
      chipTextColor = textColor ?? AppColors.onSurfaceVariant;
    }

    Widget chipContent = Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        if (icon != null) ...[
          Icon(
            icon,
            size: iconSize,
            color: chipTextColor,
          ),
          const SizedBox(width: 4),
        ],
        Text(
          label,
          style: TextStyle(
            fontSize: fontSize,
            fontWeight: FontWeight.w500,
            color: chipTextColor,
          ),
        ),
        if (onDeleted != null) ...[
          const SizedBox(width: 4),
          GestureDetector(
            onTap: onDeleted,
            child: Icon(
              deleteIcon ?? Icons.close,
              size: iconSize,
              color: chipTextColor,
            ),
          ),
        ],
      ],
    );

    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: padding,
        decoration: BoxDecoration(
          color: chipBackgroundColor,
          borderRadius: BorderRadius.circular(borderRadius ?? 20),
          border: isSelected
              ? Border.all(color: AppColors.primary, width: 1)
              : null,
        ),
        child: chipContent,
      ),
    );
  }
}

class FoodCategoryChip extends StatelessWidget {
  final String category;
  final bool isSelected;
  final VoidCallback? onTap;
  final IconData? icon;

  const FoodCategoryChip({
    super.key,
    required this.category,
    this.isSelected = false,
    this.onTap,
    this.icon,
  });

  @override
  Widget build(BuildContext context) {
    Color categoryColor = _getCategoryColor(category);

    return CustomChip(
      label: category,
      isSelected: isSelected,
      onTap: onTap,
      icon: icon,
      backgroundColor: categoryColor.withOpacity(0.1),
      selectedColor: categoryColor,
      textColor: categoryColor,
      selectedTextColor: Colors.white,
      borderRadius: 16,
    );
  }

  Color _getCategoryColor(String category) {
    switch (category.toLowerCase()) {
      case 'vegetarian':
        return AppColors.vegetarian;
      case 'vegan':
        return AppColors.vegan;
      case 'protein':
        return AppColors.protein;
      case 'dairy':
        return AppColors.dairy;
      case 'grains':
        return AppColors.grains;
      case 'fruits':
        return AppColors.fruits;
      default:
        return AppColors.primary;
    }
  }
}

class PriceRangeChip extends StatelessWidget {
  final String priceRange;
  final bool isSelected;
  final VoidCallback? onTap;

  const PriceRangeChip({
    super.key,
    required this.priceRange,
    this.isSelected = false,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return CustomChip(
      label: priceRange,
      isSelected: isSelected,
      onTap: onTap,
      icon: Icons.attach_money,
      backgroundColor: AppColors.secondaryContainer,
      selectedColor: AppColors.secondary,
      textColor: AppColors.secondary,
      selectedTextColor: AppColors.onSecondary,
    );
  }
}

class RatingChip extends StatelessWidget {
  final double rating;
  final bool isSelected;
  final VoidCallback? onTap;

  const RatingChip({
    super.key,
    required this.rating,
    this.isSelected = false,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return CustomChip(
      label: '${rating}+ ⭐',
      isSelected: isSelected,
      onTap: onTap,
      backgroundColor: AppColors.ratingGold.withOpacity(0.1),
      selectedColor: AppColors.ratingGold,
      textColor: AppColors.ratingGold,
      selectedTextColor: Colors.white,
    );
  }
}

class DeliveryTimeChip extends StatelessWidget {
  final String deliveryTime;
  final bool isSelected;
  final VoidCallback? onTap;

  const DeliveryTimeChip({
    super.key,
    required this.deliveryTime,
    this.isSelected = false,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return CustomChip(
      label: deliveryTime,
      isSelected: isSelected,
      onTap: onTap,
      icon: Icons.access_time,
      backgroundColor: AppColors.info.withOpacity(0.1),
      selectedColor: AppColors.info,
      textColor: AppColors.info,
      selectedTextColor: Colors.white,
    );
  }
}

class FilterChipGroup extends StatefulWidget {
  final List<String> options;
  final List<String> selectedOptions;
  final Function(List<String>) onSelectionChanged;
  final bool multiSelect;
  final String? title;

  const FilterChipGroup({
    super.key,
    required this.options,
    required this.selectedOptions,
    required this.onSelectionChanged,
    this.multiSelect = true,
    this.title,
  });

  @override
  State<FilterChipGroup> createState() => _FilterChipGroupState();
}

class _FilterChipGroupState extends State<FilterChipGroup> {
  late List<String> _selectedOptions;

  @override
  void initState() {
    super.initState();
    _selectedOptions = List.from(widget.selectedOptions);
  }

  void _toggleOption(String option) {
    setState(() {
      if (widget.multiSelect) {
        if (_selectedOptions.contains(option)) {
          _selectedOptions.remove(option);
        } else {
          _selectedOptions.add(option);
        }
      } else {
        _selectedOptions.clear();
        _selectedOptions.add(option);
      }
    });
    widget.onSelectionChanged(_selectedOptions);
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (widget.title != null) ...[
          Text(
            widget.title!,
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 12),
        ],
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: widget.options.map((option) {
            return CustomChip(
              label: option,
              isSelected: _selectedOptions.contains(option),
              onTap: () => _toggleOption(option),
              type: ChipType.filter,
            );
          }).toList(),
        ),
      ],
    );
  }
}
